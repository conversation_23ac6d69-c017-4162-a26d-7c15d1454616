import react from "@vitejs/plugin-react"
import Icons from "unplugin-icons/vite"
import { defineConfig } from "vite"
import svgr from "vite-plugin-svgr"

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    Icons({
      autoInstall: true
    }),
    svgr({
      // TODO: Include all SVGs here
      include: "**/*.svg?react"
    })
  ],
  resolve: {
    alias: [{ find: "@", replacement: "/src" }]
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern-compiler"
      }
    }
  }
})
