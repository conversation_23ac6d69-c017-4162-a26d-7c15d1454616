{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "types": ["vite/client"], "sourceRoot": "src", "sourceMap": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "strict": false, "noEmit": true, "module": "ES2020", "resolveJsonModule": true, "moduleResolution": "Node", "isolatedModules": true, "incremental": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"]}, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.js", "src/**/*.jsx"], "exclude": ["node_modules", "**/.*/"]}