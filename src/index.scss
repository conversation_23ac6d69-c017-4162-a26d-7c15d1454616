@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 150 70% 50%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 263 70% 50%;
    --radius: 0.5rem;
  }
}

@import "styles/font";
@import "styles/scrollbar";

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: "Inconsolata", monospace;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
  background-color: #000000;
}

body {
  color: #ffffff;
}

::-webkit-scrollbar {
  width: 0; /* Remove scrollbar space */
  background: transparent; /* Optional: just make scrollbar invisible */
}
/* Optional: show position indicator in red */
::-webkit-scrollbar-thumb {
  background: #fefefea8;
  border-radius: 10px;
}

// Section styling for smooth scroll experience
.section {
  min-height: 100vh;
  padding: 4rem 0;
  position: relative;

  @media (max-width: 768px) {
    padding: 2rem 0;
  }
}
