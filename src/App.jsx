import { useEffect } from "react"
import { IconContext } from "react-icons"

import { ShootingStars } from "@/components/ui/ShootingStars"
import { StarsBackground } from "@/components/ui/StarsBackground"

import Layout from "./components/Layout/Layout"
import AboutMe from "./pages/AboutMe/AboutMe"
import Contact from "./pages/Contact/Contact"
import Experience from "./pages/Experience/Experience"
import Skills from "./pages/Skills/Skills"

const App = () => {
  // Ensure page always loads at top
  useEffect(() => {
    // Disable scroll restoration
    if ("scrollRestoration" in history) {
      history.scrollRestoration = "manual"
    }

    // Force scroll to top on load
    window.scrollTo(0, 0)
  }, [])

  return (
    <IconContext.Provider value={{ style: { verticalAlign: "middle" } }}>
      <div className="relative min-h-screen">
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,#63e_100%)]" />
          <ShootingStars />
          <StarsBackground starDensity={0.0008} />
        </div>
        <Layout>
          <div className="relative z-10 pt-4">
            <AboutMe />
            <Skills />
            <Experience />
            <Contact />
          </div>
        </Layout>
      </div>
    </IconContext.Provider>
  )
}

export default App
