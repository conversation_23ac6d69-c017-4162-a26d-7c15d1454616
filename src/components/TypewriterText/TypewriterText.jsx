import { useEffect, useState } from "react"

import classes from "./TypewriterText.module.scss"

const TypewriterText = () => {
  const [text, setText] = useState("")
  const [showCursor, setShowCursor] = useState(true)
  const words = ["Full Stack", "Backend", "Frontend", "Django", "React"]
  const typingSpeed = 80 // Adjust the typing speed as needed
  const cursorBlinkSpeed = 500 // Adjust the cursor blink speed as needed

  useEffect(() => {
    let currentWordIndex = 0
    let currentWord = ""
    let isDeleting = false
    let typingTimeout
    let cursorInterval

    const type = () => {
      const word = words[currentWordIndex]
      const isWordComplete = isDeleting
        ? currentWord === ""
        : currentWord === word

      if (isWordComplete) {
        isDeleting = !isDeleting
        if (!isDeleting) {
          currentWordIndex = (currentWordIndex + 1) % words.length // Increment the word index
        }
        typingTimeout = setTimeout(type, 1500) // Delay between words
      } else {
        const typingSpeedForChar = isDeleting ? typingSpeed / 2 : typingSpeed
        const char = isDeleting
          ? currentWord.slice(0, -1)
          : word.slice(0, currentWord.length + 1)

        setText(char)
        currentWord = char
        typingTimeout = setTimeout(type, typingSpeedForChar)
      }
    }

    cursorInterval = setInterval(() => {
      setShowCursor((prevShowCursor) => !prevShowCursor)
    }, cursorBlinkSpeed)

    typingTimeout = setTimeout(type, 1500)

    return () => {
      clearTimeout(typingTimeout)
      clearInterval(cursorInterval)
    }
  }, [])

  return (
    <div className={classes.typewriter_text_container}>
      <div className={classes.typewriter_text}>{text}</div>
      {showCursor && <span className={classes.cursor}>&nbsp;</span>}
      &nbsp;Developer
    </div>
  )
}

export default TypewriterText
