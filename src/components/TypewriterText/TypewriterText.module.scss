$content-size: 7vw;

.typewriter_text_container {
  position: relative;
  font-size: $content-size;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 100px);
}

.typewriter_text {
  animation: blink-caret 0.75s step-end infinite;
  display: inline;
  min-height: fit-content;
}

@keyframes blink-caret {
  from,
  to {
    border-right-color: transparent;
  }
  50% {
    border-right-color: #fff;
  }
}

.cursor {
  display: inline-block;
  width: 8px;
  height: $content-size;
  background-color: #fff;
  animation: blink 1s infinite;
}

@keyframes blink {
  from,
  to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
