import { useEffect, useRef, useState } from "react"
import classNames from "classnames"

import items from "./SideNavItems.json"

import * as classes from "./SideNav.module.scss"

const SideNav = () => {
  const [activeItemId, setActiveItemId] = useState("about_me")
  const sections = useRef([])
  const scrollContainerRef = useRef(null)

  const handleClick = (id) => {
    setActiveItemId(id)

    const element = document.getElementById(id)
    if (element && scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: element.offsetTop,
        behavior: "smooth"
      })
    }
  }

  const handleScroll = () => {
    if (!scrollContainerRef.current) return

    const scrollTop = scrollContainerRef.current.scrollTop
    const viewportHeight = scrollContainerRef.current.clientHeight
    const scrollThreshold = viewportHeight * 0.6 // 60% of viewport height

    let newActiveSection = null

    for (const section of sections.current) {
      const sectionTop = section.offsetTop - scrollTop

      if (sectionTop <= scrollThreshold) {
        newActiveSection = section.id
      } else {
        break
      }
    }

    if (newActiveSection) {
      setActiveItemId(newActiveSection)
    }
  }

  useEffect(() => {
    scrollContainerRef.current = document.getElementById("layout")
    sections.current = Array.from(document.querySelectorAll("[data-section]"))

    if (scrollContainerRef.current) {
      scrollContainerRef.current.addEventListener("scroll", handleScroll)
      // Trigger initial check
      handleScroll()
    }

    return () => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.removeEventListener("scroll", handleScroll)
      }
    }
  }, [])

  return (
    <nav className={classes.side_nav_container}>
      {items.map((item, index) => {
        const styles = classNames(classes.side_nav_item_container, {
          [classes.active_side_nav_item_container]: activeItemId === item.id
        })
        return (
          <div className={styles} key={`item-${index}`}>
            <span onClick={() => handleClick(item.id)}>{item?.label}</span>
          </div>
        )
      })}
    </nav>
  )
}

export default SideNav
