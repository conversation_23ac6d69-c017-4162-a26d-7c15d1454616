.side_nav_container {
	position: fixed;
	right: 0;
	justify-content: space-between;
	display: flex;
	flex-direction: column;
	z-index: 20;
	user-select: none;
	padding-block: 100px;
	flex-wrap: nowrap;

	.side_nav_item_container {
		transform: rotate(-90deg);
		display: inline-block;
		width: 100px;
		margin: 60px 0;
		font-weight: 700;
		cursor: pointer;
		white-space: nowrap;
		color: #ffffff80;
		transition: all 0.2s ease;
		text-align: center;
		flex-grow: 1;

		&:hover {
			color: #ffffff;
		}
	}

	.active_side_nav_item_container {
		color: #ffffff;
	}

	@media screen and (max-width: 700px) {
		display: none;
	}
}
