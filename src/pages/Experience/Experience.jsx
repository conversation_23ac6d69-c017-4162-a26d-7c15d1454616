import { useState } from "react"
import {
  Building2,
  Calendar,
  ChevronDown,
  ChevronUp,
  MapPin
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"

const Experience = () => {
  const [expandedCard, setExpandedCard] = useState(null)

  const experiences = [
    {
      id: 1,
      position: "Senior Full Stack Developer",
      company: "TechCorp Solutions",
      duration: "2023 - Present",
      location: "San Francisco, CA",
      type: "Full-time",
      description:
        "Leading development of scalable web applications using React, Node.js, and cloud technologies. Mentoring junior developers and architecting microservices solutions.",
      technologies: [
        "React",
        "Node.js",
        "AWS",
        "Docker",
        "PostgreSQL",
        "TypeScript"
      ],
      achievements: [
        "Improved application performance by 40%",
        "Led a team of 5 developers",
        "Implemented CI/CD pipelines reducing deployment time by 60%"
      ]
    },
    {
      id: 2,
      position: "Full Stack Developer",
      company: "InnovateLab",
      duration: "2021 - 2023",
      location: "Austin, TX",
      type: "Full-time",
      description:
        "Developed and maintained multiple client-facing applications. Collaborated with cross-functional teams to deliver high-quality software solutions.",
      technologies: [
        "React",
        "Python",
        "Django",
        "PostgreSQL",
        "Redis",
        "JavaScript"
      ],
      achievements: [
        "Built 3 major client applications from scratch",
        "Reduced bug reports by 35% through comprehensive testing",
        "Optimized database queries improving response time by 50%"
      ]
    },
    {
      id: 3,
      position: "Frontend Developer",
      company: "StartupXYZ",
      duration: "2020 - 2021",
      location: "Remote",
      type: "Contract",
      description:
        "Focused on creating responsive and interactive user interfaces. Worked closely with designers to implement pixel-perfect designs.",
      technologies: ["React", "JavaScript", "Sass", "Webpack", "Git", "Figma"],
      achievements: [
        "Increased user engagement by 25%",
        "Implemented responsive design for mobile users",
        "Collaborated with UX team to improve user experience"
      ]
    },
    {
      id: 4,
      position: "Junior Web Developer",
      company: "WebSolutions Inc",
      duration: "2019 - 2020",
      location: "New York, NY",
      type: "Full-time",
      description:
        "Started my professional journey building websites and learning modern web development practices. Gained experience in both frontend and backend technologies.",
      technologies: ["HTML", "CSS", "JavaScript", "PHP", "MySQL", "jQuery"],
      achievements: [
        "Completed 15+ client projects successfully",
        "Learned modern development workflows",
        "Contributed to team knowledge sharing sessions"
      ]
    }
  ]

  const toggleExpanded = (id) => {
    setExpandedCard(expandedCard === id ? null : id)
  }

  return (
    <section id="experience" className="section">
      <div className="container mx-auto px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Experience
          </h2>
          <p className="text-muted-foreground text-sm md:text-base">
            My professional journey through the tech landscape
          </p>
        </div>

        <div className="relative mx-auto max-w-4xl">
          {/* Timeline line */}
          <div className="absolute inset-y-0 left-8 w-0.5 bg-gradient-to-b from-purple-500 via-blue-500 to-purple-500 opacity-30" />

          <div className="space-y-8">
            {experiences.map((experience) => (
              <div key={experience.id} className="relative">
                {/* Timeline dot */}
                <div className="border-background absolute left-6 top-6 h-4 w-4 rounded-full border-2 bg-gradient-to-r from-purple-500 to-blue-500 shadow-lg" />

                <div className="ml-16">
                  <Card className="group border-white/10 bg-white/5 backdrop-blur-sm transition-all duration-300 hover:border-purple-500/50 hover:bg-white/10 hover:shadow-lg hover:shadow-purple-500/25">
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <CardTitle className="text-lg font-semibold text-white">
                            {experience.position}
                          </CardTitle>
                          <div className="text-muted-foreground flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <Building2 className="size-4" />
                              <span className="text-blue-400">
                                {experience.company}
                              </span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {experience.type}
                            </Badge>
                          </div>
                          <div className="text-muted-foreground flex items-center gap-4 text-xs">
                            <div className="flex items-center gap-1">
                              <Calendar className="size-3" />
                              {experience.duration}
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="size-3" />
                              {experience.location}
                            </div>
                          </div>
                        </div>

                        <button
                          onClick={() => toggleExpanded(experience.id)}
                          className="rounded-full p-2 transition-colors hover:bg-white/10"
                        >
                          {expandedCard === experience.id ? (
                            <ChevronUp className="size-4 text-gray-400" />
                          ) : (
                            <ChevronDown className="size-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <CardDescription className="mb-4 text-sm text-gray-300">
                        {experience.description}
                      </CardDescription>

                      {/* Technologies */}
                      <div className="mb-4">
                        <h4 className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-400">
                          Technologies
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {experience.technologies.map((tech) => (
                            <Badge
                              key={tech}
                              variant="skill"
                              className="text-xs"
                            >
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Expandable achievements */}
                      <div className="overflow-hidden">
                        <div className="border-t border-white/10 pt-4">
                          <h4 className="mb-3 text-xs font-medium uppercase tracking-wide text-gray-400">
                            Key Achievements
                          </h4>
                          <ul className="space-y-2">
                            {experience.achievements.map((achievement, idx) => (
                              <li
                                key={idx}
                                className="flex items-start gap-2 text-sm text-gray-300"
                              >
                                <span className="mt-1 text-purple-400">▸</span>
                                {achievement}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Experience
