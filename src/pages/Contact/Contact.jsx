import { useForm } from "react-hook-form"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/fa"
import emailjs from "@emailjs/browser"
import { zodResolver } from "@hookform/resolvers/zod"
import { Clock, Mail, Send } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

import schema from "./schema"

const Contact = () => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange"
  })

  const onSubmit = async (data) => {
    try {
      await emailjs.send(
        import.meta.env.VITE_CONTACT_MESSAGE_SERVICE_ID,
        import.meta.env.VITE_CONTACT_MESSAGE_TEMPLATE_ID,
        {
          from_name: data.name,
          from_email: data.email,
          message: data.message
        },
        {
          publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
          limitRate: {
            id: "app",
            throttle: 10_000
          }
        }
      )
      reset()
    } catch (error) {
      console.error("Failed to send message:", error)
    }
  }

  const socialLinks = [
    {
      name: "GitHub",
      icon: FaGithub,
      url: import.meta.env.VITE_GITHUB_LINK,
      color: "hover:text-gray-300"
    },
    {
      name: "LinkedIn",
      icon: FaLinkedin,
      url: import.meta.env.VITE_LINKEDIN_LINK,
      color: "hover:text-primary"
    },
    {
      name: "Twitter",
      icon: FaTwitter,
      url: import.meta.env.VITE_TWITTER_LINK,
      color: "hover:text-primary"
    }
  ]

  return (
    <section id="contact" className="section relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="blur-3xl-3xl absolute left-1/4 top-1/3 h-48 w-48 rounded-full bg-gradient-to-r from-primary/30 to-primary/20" />
        <div className="blur-3xl-3xl absolute bottom-1/3 right-1/4 size-64 rounded-full bg-gradient-to-r from-primary/20 to-primary/30" />
      </div>

      <div className="container relative z-10 mx-auto max-w-6xl px-8">
        <div className="mb-12 text-center">
          <h2 className="mb-3 text-2xl font-bold text-white md:text-3xl lg:text-4xl">
            Let&apos;s Connect
          </h2>
          <p className="text-muted-foreground mx-auto max-w-lg text-sm md:text-base">
            Ready to bring your ideas to life? Let&apos;s start a conversation.
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2 lg:gap-12">
          {/* Left Side - Contact Info */}
          <div className="space-y-6">
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg text-white">
                  <Mail className="text-primary size-5" />
                  Get in Touch
                </CardTitle>
                <CardDescription className="text-sm text-gray-300">
                  Whether you have a question, an exciting project idea, or j to
                  want to say hello, I&apos;d love to hear from you!
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base text-white">
                  <Clock className="text-primary size-4" />
                  Quick Response
                </CardTitle>
                <CardDescription className="text-sm text-gray-300">
                  I typically respond to messages within 24 hours. Look ing
                  forward to hearing from you!
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Social Links */}
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="mb-4 text-base text-white">
                  Connect on Social Media
                </CardTitle>
                <div className="flex gap-4">
                  {socialLinks.map((social) => {
                    const IconComponent = social.icon
                    return (
                      <a
                        key={social.name}
                        href={social.url}
                        target="_blank"
                        rel="noreferrer"
                        className={cn(
                          "hover:bg-white/e-110 flex items-center justify-center rounded-lg bg-white/5 p-3 backdrop-blur-sm transition-all duration-200 hover:scale-110",
                          social.color
                        )}
                      >
                        <IconComponent className="size-5w-5" />
                      </a>
                    )
                  })}
                </div>
              </CardHeader>
            </Card>
          </div>

          {/* Right Side - Contact Form */}
          <div>
            <Card className="border-white/10 bg-white/5 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg text-white">
                  Send me a message
                </CardTitle>
                <CardDescription className="text-sm text-gray-300">
                  Fill out the form below and I&apos;ll get back to you soon.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-300">
                      Your Name
                    </label>
                    <Input
                      className={cn(
                        "order-white/20 bg-white/1e/20 text-white placeholder:text-gray-400 focus:border-primary focus:ring-primary/20",
                        errors.name && "border-red-500 focus:border-red-500"
                      )}
                      placeholder="Enter your full name"
                      {...register("name")}
                    />
                    {errors.name && (
                      <p className="text-red-40 text-xs">
                        {errors.name.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-300">
                      Email Address
                    </label>
                    <Input
                      type="email"
                      className={cn(
                        "order-white/20 bg-white/1e/20 text-white placeholder:text-gray-400 focus:border-primary focus:ring-primary/20",
                        errors.email && "border-red-500 focus:border-red-500"
                      )}
                      placeholder="<EMAIL>"
                      {...register("email")}
                    />
                    {errors.email && (
                      <p className="text-red-40 text-xs">
                        {errors.email.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="text-xs font-medium text-gray-300">
                      Message
                    </label>
                    <Textarea
                      className={cn(
                        "focus:ring-primary/20px] min-h-[100px] border-white/20 bg-white/10 text-white placeholder:text-gray-400 focus:border-primary",
                        errors.message && "border-red-500 focus:border-red-500"
                      )}
                      placeholder="Tell me about your project or just say hello..."
                      {...register("message")}
                    />
                    {errors.message && (
                      <p className="text-red-40 text-xs">
                        {errors.message.message}
                      </p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    variant="gradient"
                    className="w-full"
                    disabled={!isValid}
                  >
                    <span className="flex items-center justify-center gap-2">
                      {isValid ? (
                        <>
                          Send Message
                          <Send className="size-4w-4" />
                        </>
                      ) : (
                        <>
                          Fill out the form
                          <Mail className="size-4w-4" />
                        </>
                      )}
                    </span>
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Contact
