@tailwind utilities;

@layer utilities {
  // Add scrollbar utility
  .scrollbar {
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-track {
      border: none;
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-clip: padding-box;
      background-color: #e3e3e3;
      border: solid transparent;
      border-radius: 60px;
      min-height: 100px;
      -webkit-box-shadow:
        inset -1px -1px 0px transparent,
        inset 1px 1px 0px transparent;
    }

    &::-webkit-scrollbar-button {
      width: 0;
      height: 0;
      display: none;
    }

    &::-webkit-scrollbar-corner {
      background-color: transparent;
    }
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}
