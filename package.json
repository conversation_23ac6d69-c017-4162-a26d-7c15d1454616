{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint-fix": "eslint --fix .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.13.3", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.0", "react-icons": "^5.5.0", "react-redux": "^8.1.2", "react-use": "^17.5.1", "sass": "^1.79.4", "tailwind-merge": "^2.6.0", "vite-plugin-svgr": "^4.2.0", "zod": "^3.23.8"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.20", "eslint": "^8.44.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.4", "husky": "^9.1.6", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.13", "unplugin-icons": "^0.19.3", "vite": "^4.4.0"}}